import { useEffect, useRef, useState } from "react";
import { css, keyframes, styled } from "styled-components";
import upImage from "../../../assets/images/button/up.png";
import infoDot from "../../../assets/images/dot-icons/infoDot.svg";
import userDot from "../../../assets/images/dot-icons/userDot.svg";
import Container from "../../../components/Container";
import Datepicker from "../../../components/Datepicker/Datepicker";
import Dropdown from "../../../components/Dropdown/Dropdown";
import Image from "../../../components/Image";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";
import { DefaultPermissions } from "../../../constants/permissions";
import { PersonalDataDTO } from "../../../models/DTOs/newEmployee/PersonalDataDTO";
import { getBirthDateFromEGN } from "../../../services/employees/employeesService";
import Translator, { translate } from "../../../services/language/Translator";
import { useEnums } from "../../EnumContext";
import { useUserEmployee } from "../../UserEmployeeContext";

const FormContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const PersonalInfoSection = styled(Container)`
  display: flex;
  flex-direction: column;
`;

const FormRow = styled(Container)`
  display: flex;
  flex-direction: row;
  gap: 0.3rem;
  width: 100%;
`;

const FormField = styled(Container)`
  flex: 1;
  min-width: 0;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  margin-left: 0.5rem;
  color: #333;
  white-space: nowrap;
`;

const SectionSeparator = styled.div`
  height: 1px;
  display: flex;
  background-color: white;
  width: 100%;
  margin-left: 0.5rem;
  margin-top: 0.3rem;
`;

const DropdownContainer = styled.div`
  position: relative;
  width: 100%;
  min-width: 0;
  max-width: 100%;
  flex-shrink: 0;
  margin-top: 0.2rem;
  margin-bottom: 0.2rem;
`;

const DropdownFormField = styled(Container)`
  flex: 1;
  min-width: 0;
  max-width: 100%;
  width: 100%;
  flex-shrink: 0;
  flex-grow: 0;
`;

const DropdownFormRow = styled(Container)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.3rem;
  width: 100%;
`;

const HeaderWrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  min-width: 0;
  border: 0;
  border-radius: 1.9rem;
  padding: 0.7rem 2.1rem 0.7rem 1.2rem;
  transition: 0.4s;
  background: var(--textbox-color);
  outline: none;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;

  /* Force all child elements to respect width constraints */
  * {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &:first-child {
    border-radius: ${(props) =>
      props.isOpen ? "1.625rem 1.625rem 0 0" : "2rem"};
  }
  data-testid: "header-wrapper";
`;

const HeaderImage = styled(Container)<{ isClicked: boolean }>`
  position: absolute;
  background-size: cover;
  height: 1rem;
  width: 1rem;
  right: 1rem;
  top: 40%;
  cursor: pointer;
  background-image: url(${upImage});
  transition-duration: 0.5s;
  ${({ isClicked }) =>
    !isClicked &&
    `transform:rotate(180deg);
    transition-duration: 0.5s;
    background-image: url(${upImage});
    top: 45%;
  `}
  data-testid: "header-image";
`;

const DropdownBody = styled(Dropdown.Body)`
  width: 100%;
  max-height: 20rem;
  overflow-y: auto;
  scrollbar-width: none;
  overflow-x: hidden;
  scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track-color);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color);
    border-radius: 10px;
    border: 2px solid var(--scrollbar-track-color);
  }
  data-testid: "dropdown-body";
`;

const Wrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  padding: 0.5rem 1rem 0.5rem 1.5rem;
  background: var(--textbox-color);
  outline: none;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--company-dropdown-closed-color);
  }

  &:last-child {
    border-radius: 0 0 2rem 2rem;
    padding-bottom: 2rem;
  }

  animation: ${({ isOpen }) =>
    isOpen
      ? css`
          ${fadeIn} 0.3s ease-in
        `
      : css`
          ${fadeOut} 0.3s ease-out
        `};
  data-testid: "company-wrapper";
`;

const fadeIn = keyframes`
  from {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
    to {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.625rem;
    }
`;

const fadeOut = keyframes`
  from {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.188rem;
    }
    to {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  onChange?: () => void;
  data?: PersonalDataDTO;
}

const NewPersonalData = ({ onValidation, onChange, data }: Props) => {
  const isInitialRender = useRef(true);
  const { userEmployee } = useUserEmployee();

  const hasEmployeeWritePermission = userEmployee.permissions.includes(
    DefaultPermissions.Employees.Write
  );
  const [formData, setFormData] = useState<PersonalDataDTO>({
    firstName: data?.firstName || "",
    secondName: data?.secondName || "",
    lastName: data?.lastName || "",
    egn: data?.egn || "",
    birthDate: data?.birthDate ? new Date(data.birthDate) : undefined,
    contractType:
      data?.contractType !== undefined ? data.contractType : undefined,
    birthPlace: data?.birthPlace || "",
    iban: data?.iban || "",
    email: data?.email || "",
  });

  const { appointmentTypes } = useEnums();
  const appointmentTypesWithRequiredEGN = [0, 1, 2, 3, 9, 10, 11, 12];

  const filteredAppointmentTypes = appointmentTypes.filter(
    (type) => ![9, 10, 11, 12].includes(type.identifier)
  );

  const [isContractTypeDropdownOpen, setIsContractTypeDropdownOpen] =
    useState(false);

  useEffect(() => {
    console.log(formData.birthDate);
    const isValid =
      formData.firstName.trim() !== "" &&
      formData.contractType !== undefined &&
      (!appointmentTypesWithRequiredEGN.includes(formData.contractType) ||
        formData.egn.trim() !== "");

    if (onValidation) {
      onValidation(isValid, formData);
    }
  }, [formData]);

  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    if (!formData.egn) {
      setFormData((prev) => ({ ...prev, birthDate: undefined }));
      return;
    }
    const birthDate = getBirthDateFromEGN(formData.egn);
    if (birthDate) {
      setFormData((prev) => ({ ...prev, birthDate }));
    }
  }, [formData.egn]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev: PersonalDataDTO) => {
      const newData = {
        ...prev,
        [name]: value,
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
  };

  const handleDateChange = (value: Date) => {
    setFormData((prev: PersonalDataDTO) => {
      const newData = {
        ...prev,
        birthDate: value,
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
  };

  const handleContractTypeSelect = (value: number) => {
    setFormData((prev: PersonalDataDTO) => {
      const newData = {
        ...prev,
        contractType: value,
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
    setIsContractTypeDropdownOpen(false);
  };

  const getContractTypeLabel = () => {
    const option = filteredAppointmentTypes.find(
      (opt) => opt.identifier === formData.contractType
    );
    return option ? option.name : "Тип договор";
  };

  const isOpened = (isOpen: boolean) => {
    setIsContractTypeDropdownOpen(isOpen);
  };

  return (
    <FormContainer>
      <PersonalInfoSection>
        <FormField>
          <Textbox
            name="firstName"
            label="Name"
            value={formData.firstName}
            handleChange={handleChange}
          />
        </FormField>

        <FormField>
          <Textbox
            name="secondName"
            label="Surname"
            value={formData.secondName}
            handleChange={handleChange}
          />
        </FormField>

        <FormField>
          <Textbox
            name="lastName"
            label="Last name"
            value={formData.lastName}
            handleChange={handleChange}
          />
        </FormField>
      </PersonalInfoSection>

      <SectionHeader>
        <Image src={infoDot} data-testid="info-dot-image" />
        <SectionTitle>{translate("strBaseData")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="egn"
            label="EGN / LNCH"
            value={formData.egn}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Datepicker
            label="Date of birth"
            initialDate={formData.birthDate}
            onSelectDate={handleDateChange}
          />
        </FormField>
      </FormRow>

      <DropdownFormRow>
        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={hasEmployeeWritePermission ? isOpened : undefined}
              data-testid="dropdown"
            >
              <Dropdown.Header
                data-testid="dropdown-header"
                style={{
                  cursor: hasEmployeeWritePermission
                    ? "pointer"
                    : "not-allowed",
                  opacity: hasEmployeeWritePermission ? 1 : 0.6,
                }}
              >
                <HeaderWrapper
                  isOpen={isContractTypeDropdownOpen}
                  data-testid="header-wrapper"
                >
                  <Translator getString={getContractTypeLabel()} />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isContractTypeDropdownOpen}
                  data-testid="header-image"
                ></HeaderImage>
              </Dropdown.Header>
              {hasEmployeeWritePermission && (
                <DropdownBody data-testid="dropdown-body">
                  {filteredAppointmentTypes.map((c) => (
                    <Wrapper
                      isOpen={isContractTypeDropdownOpen}
                      key={c?.identifier}
                      onClick={() => handleContractTypeSelect(c.identifier)}
                      data-testid={`contract-reason-wrapper-${c?.identifier}`}
                    >
                      <Translator getString={c.name} />
                    </Wrapper>
                  ))}
                </DropdownBody>
              )}
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>
        <FormField>
          <Textbox
            name="birthPlace"
            label="Place of birth"
            value={formData.birthPlace}
            handleChange={handleChange}
          />
        </FormField>
      </DropdownFormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="iban"
            label="IBAN"
            value={formData.iban}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <SectionHeader>
        <Image src={userDot} data-testid="info-dot-image" />
        <SectionTitle>{translate("strProfileCreationDate")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="email"
            label="E-mail"
            value={formData.email}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>
    </FormContainer>
  );
};

export default NewPersonalData;
