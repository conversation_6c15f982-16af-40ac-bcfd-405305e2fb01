import { styled } from "styled-components";
import MainWindowContainer from "../../components/MainWindowContainer";
import Container from "../../components/Container";
import { useEffect, useState } from "react";
import {
  authenticatedPost,
  authenticatedPut,
} from "../../services/worktimeConnectionService";
import { EventType } from "../../models/DTOs/absence/EventType";
import { AddAbsenceRequest } from "../../models/DTOs/absence/AddAbsenceRequest";
import { AbsenceStatus as AbsenceStatusEnum } from "../../models/DTOs/absence/AbsenceStatus";
import { AbsenceStatus } from "../../models/DTOs/absence/AbsenceStatus";
import { onPayrollsLoaded, selectPayrolls } from "../payroll/payrollsActions";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { useAuth } from "../authentication/AuthContext";
import { AbsenceHospitalDTO } from "../../models/DTOs/absence/AbsenceHospitalDTO";
import { toast } from "react-toastify";
import { translate } from "../../services/language/Translator";
import {
  onPayrollAbsenceRemoved,
  onPayrollAbsenceUpdated,
} from "../payroll/payrollsActions";
import { useModal } from "../../components/PopUp/ActionModalContext";
import { Result } from "../../models/Results/Result";
import { ValidationException } from "../../models/Exceptions/ValidationException";
import { useMenu } from "../MenuContext";
import AbsenceButtons from "./components/AbsenceButtons";
import AbsenceForm, { FormData } from "./components/AbsenceForm";
import AbsenceList from "./components/AbsenceList";
import AbsenceDetails from "./components/AbsenceDetails";
import { EditAbsenceResult } from "../../models/DTOs/absence/EditAbsenceResult";
import { useAbsence } from "./AbsenceContext";
import { useCompany } from "../companies/CompanyContext";
import { AbsenceInfo } from "../../components/CalendarComponent/types/AbsenceInfo";
import { useUserEmployee } from "../UserEmployeeContext";
import { DefaultPermissions } from "../../constants/permissions";
import { getLeaveUpdatedMessage } from "../../utils/leaveTypeUtils";
import {
  DoesTypeOfAppointmentNeededConfirmation,
  TypeOfAppointment,
} from "../enums/TypeOfAppointment";

const MainContainer = styled(MainWindowContainer)`
  width: 100%;
  align-items: center;
`;

const AbsenceView = styled(Container)`
  display: flex;
  flex-direction: column;
`;

const parseDateString = (dateStr: string): Date => {
  const [day, month, year] = dateStr.split(".");
  return new Date(Date.UTC(Number(year), Number(month) - 1, Number(day)));
};

const AbsencesSideMenu = () => {
  const options = ["Paid Leave", "Unpaid Leave"];
  const sickOptions = ["Sick Leave", "Maternity Leave"];

  const [absencesVisible, setAbsencesVisible] = useState(true);
  const { openModal } = useModal();
  const [isLoading, setIsLoading] = useState(false);
  const payrollsState = useAppSelector(selectPayrolls);
  const { viewData, isOpen } = useMenu();
  const selectedPayroll = viewData?.selectedPayroll;
  const selectedYear = viewData?.selectedYear;
  const selectedMonth = viewData?.selectedMonth;

  const { user } = useAuth();
  const { userEmployee } = useUserEmployee();
  const { toggleMenu, closeMenu } = useMenu();
  const dispatch = useAppDispatch();
  const { company } = useCompany();
  const {
    selectedAbsence,
    setSelectedAbsence,
    isEditing,
    setIsEditing,
    resetAbsence,
  } = useAbsence();

  const [showForm, setShowForm] = useState(false);
  const [editActivationToggle, setEditActivationToggle] = useState(false);

  const isAdmin = userEmployee.permissions.includes(
    DefaultPermissions.Attendances.Write
  );

  const isMyAbsence = selectedAbsence?.userId === user.userId;

  useEffect(() => {
    setShowForm(
      !selectedAbsence ||
        isEditing ||
        ((selectedAbsence.status === AbsenceStatus.Pending ||
          selectedAbsence.status == AbsenceStatus.EditedByEmployee ||
          selectedAbsence.status ===
            AbsenceStatus.DeletedByUserAfterApproval) &&
          (isMyAbsence || isAdmin))
    );
  }, [selectedAbsence, isEditing]);

  useEffect(() => {
    if (!isOpen) {
      const timeoutId = setTimeout(() => {
        resetAbsence();
        setAbsencesVisible(true);
      }, 400);

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [isOpen, resetAbsence]);

  useEffect(() => {
    dispatch(onPayrollsLoaded(company.id));
  }, [company.id]);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setSelectedAbsence(null);
        resetForm();
      }
    };

    document.addEventListener("keydown", handleEscKey);

    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, []);

  useEffect(() => {
    if (selectedAbsence) {
      setAbsencesVisible(!selectedAbsence.isHospital);
    } else {
      setAbsencesVisible(true);
    }
  }, [selectedAbsence]);

  const handleTabChange = (isAbsencesVisible: boolean) => {
    if (absencesVisible === isAbsencesVisible) {
      return;
    }

    if (!selectedAbsence) {
      setAbsencesVisible(isAbsencesVisible);
      return;
    }

    openModal({
      type: "warning",
      title: absencesVisible
        ? translate("strChangingAbsenceTypeWarning")
        : translate("strChangingHospitalTypeWarning"),
      confirmLabel: translate("Yes"),
      cancelLabel: translate("No"),
      onCancel: () => {},
      onConfirm: () => {
        setAbsencesVisible(isAbsencesVisible);
        setIsEditing(true);
        setEditActivationToggle((prev) => !prev);
        if (selectedAbsence) {
          const updatedAbsence = {
            ...selectedAbsence,
            comment: selectedAbsence.isHospital
              ? selectedAbsence.sickNote ?? ""
              : selectedAbsence.comment ?? "",
            sickNote: selectedAbsence.isHospital
              ? selectedAbsence.comment ?? ""
              : selectedAbsence.sickNote ?? "",
            isHospital: !isAbsencesVisible,
            typeIdentifier: isAbsencesVisible
              ? EventType.ПлатенГодишенОтпуск
              : EventType.Болничен,
          };
          setSelectedAbsence(updatedAbsence);
        }
      },
    });
  };

  const getEventType = (selectedOption: string) => {
    if (selectedOption === "Paid Leave") {
      return EventType.ПлатенГодишенОтпуск;
    } else if (selectedOption === "Sick Leave") {
      return EventType.Болничен;
    } else if (selectedOption === "Maternity Leave") {
      return EventType.БолниченПоБременност;
    }

    return EventType.НеплатенБезСтажОтОсигурен;
  };

  const handleSubmit = async (formData: FormData) => {
    setIsLoading(true);
    if (!absencesVisible) {
      addAbsenceToPayrolls(formData, true);
      return;
    }

    if (
      absencesVisible &&
      userEmployee.payrolls !== undefined &&
      userEmployee.payrolls.length > 1
    ) {
      openModal({
        type: "info",
        title: translate("strAddAbsenceToAllPayrolls"),
        requireMessage: false,
        confirmLabel: translate("Yes"),
        cancelLabel: translate("No"),
        onConfirm: () => {
          addAbsenceToPayrolls(formData, true);
        },
        onCancel: () => {
          addAbsenceToPayrolls(formData, false);
        },
      });
    } else {
      addAbsenceToPayrolls(formData, false);
    }
  };

  const addAbsenceToPayrolls = async (
    formData: FormData,
    addAbsenceToAllPayrolls: boolean
  ) => {
    const request: AddAbsenceRequest = {
      fromDate: parseDateString(formData.startDate ?? ""),
      toDate: parseDateString(formData.endDate ?? ""),
      typeIdentifier: getEventType(formData.selectedOption),

      payrollIds: addAbsenceToAllPayrolls
        ? payrollsState.payrolls
            .filter((p) => p.employee.userId === user.userId)
            .map((p) => p.id)
        : selectedPayroll?.workTimeId
        ? [selectedPayroll.workTimeId]
        : [],
      comment: formData.comment.length > 0 ? formData.comment : undefined,
      status: DoesTypeOfAppointmentNeededConfirmation(
        selectedPayroll?.contractType ?? TypeOfAppointment.EmploymentContracts
      )
        ? AbsenceStatusEnum.Pending
        : AbsenceStatusEnum.Approved,
    };

    try {
      const absenceAddedResult = await authenticatedPost<
        Result<AbsenceHospitalDTO[]>
      >("absence", request);
      absenceAddedResult.value.map((absence) =>
        dispatch(onPayrollAbsenceUpdated(absence))
      );

      if (absenceAddedResult.validationWarning.hasValidationWarnings) {
        openModal({
          type: "info",
          title: translate("strAbsenceExistsOnSickLeaveRequest"),
          requireMessage: false,
          confirmLabel: translate("Ok"),
          onConfirm: () => {
            toast.success(
              absencesVisible
                ? translate("strAbsenceAddedSuccessfully")
                : translate("strSickLeaveAddedSuccessfully")
            );

            closeMenu();
            resetForm();
          },
        });
      }

      resetForm();
    } catch (error) {
      if (error instanceof ValidationException) {
        openModal({
          type: "error",
          title: translate(error.validationErrors[0]),
          requireMessage: false,
          confirmLabel: translate("Ok"),
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    resetAbsence();
    setAbsencesVisible(true);
  };

  const handleAbsenceSelect = (absence: AbsenceInfo | null) => {
    setSelectedAbsence(absence);
  };

  const handleEditAbsence = async (formData: FormData) => {
    if (!selectedAbsence) return;
    setIsLoading(true);

    try {
      const editResult = await authenticatedPut<Result<EditAbsenceResult>>(
        `absence/edit`,
        {
          id: selectedAbsence.id,
          typeIdentifier: getEventType(formData.selectedOption),
          fromDate: parseDateString(formData.startDate ?? ""),
          toDate: parseDateString(formData.endDate ?? ""),
          comment: formData.comment,
          sickNote: formData.comment,
          payrollId: selectedAbsence.payrollId,
          status: selectedAbsence.status,
          isAdminEdit: isAdmin,
        }
      );

      if (
        editResult.value.oldAbsence &&
        editResult.value.newAbsence &&
        editResult.value.oldAbsence.id !== editResult.value.newAbsence.id
      ) {
        if (editResult.value.oldAbsence.id) {
          dispatch(onPayrollAbsenceRemoved(editResult.value.oldAbsence.id));
        }
      }

      resetForm();

      if (editResult.validationWarning.hasValidationWarnings) {
        if (!editResult.validationWarning.validationWarnings.includes(30006)) {
          dispatch(onPayrollAbsenceUpdated(editResult.value.newAbsence));
          openModal({
            type: "info",
            title: translate("strAbsenceExistsOnSickLeaveRequest"),
            requireMessage: false,
            confirmLabel: translate("Ok"),
            onConfirm: () => {
              toggleMenu();
              resetForm();
            },
          });
        } else {
          openModal({
            type: "info",
            title: translate("strAbsenceHasStartedAndCannotBeEdited"),
            requireMessage: false,
            confirmLabel: translate("Ok"),
            onConfirm: () => {
              toggleMenu();
              resetForm();
            },
          });
        }
      } else {
        dispatch(onPayrollAbsenceUpdated(editResult.value.newAbsence));
        toast.success(getLeaveUpdatedMessage(selectedAbsence));
      }
    } catch (error) {
      if (error instanceof ValidationException) {
        openModal({
          type: "error",
          title: translate(error.validationErrors[0]),
          requireMessage: false,
          confirmLabel: translate("Ok"),
          onConfirm: () => {
            toggleMenu();
          },
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToForm = () => {
    toggleMenu();

    setTimeout(() => {
      resetAbsence();
      resetForm();
    }, 400);
  };

  const handleEditButtonClick = () => {
    setIsEditing(true);
  };

  return (
    <MainContainer data-testid="absence-main-container">
      <AbsenceView data-testid="absence-view">
        {showForm ? (
          <>
            <AbsenceButtons
              absencesVisible={absencesVisible}
              onTabChange={handleTabChange}
            />
            <AbsenceForm
              options={absencesVisible ? options : sickOptions}
              isLoading={isLoading}
              selectedAbsence={selectedAbsence}
              absencesVisible={absencesVisible}
              initialMonth={selectedMonth}
              initialYear={selectedYear}
              onSubmit={handleSubmit}
              onEdit={handleEditAbsence}
              isEditing={isEditing}
              isAdmin={isAdmin}
              editActivationToggle={editActivationToggle}
            />
            <AbsenceList
              selectedAbsence={selectedAbsence}
              selectedYear={selectedYear}
              selectedMonth={selectedMonth}
              onAbsenceSelect={handleAbsenceSelect}
              isAdmin={isAdmin}
            />
          </>
        ) : (
          <AbsenceDetails
            absence={selectedAbsence!}
            onBack={handleBackToForm}
            onEdit={handleEditButtonClick}
          />
        )}
      </AbsenceView>
    </MainContainer>
  );
};

export default AbsencesSideMenu;
